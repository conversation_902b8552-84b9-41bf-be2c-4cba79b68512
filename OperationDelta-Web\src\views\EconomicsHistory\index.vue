<template>
  <div class="economics-history-page">
    <!-- 页面标题区 -->
    <div class="container">
      <n-card class="page-header" :bordered="false">
        <template #header>
          <div class="header-content">
            <!-- 左侧标题区 -->
            <div class="title-section">
              <div class="title-row">
                <n-icon :size="24" class="title-icon">
                  <HistoryIcon />
                </n-icon>
                <h1>三角洲经济学编年史</h1>
              </div>
              <p class="subtitle">记录游戏经济系统的重要变迁时刻</p>
            </div>
            
            <!-- 右侧控制区 -->
            <div class="control-section">
              <n-space>
                <n-select
                  v-model:value="currentSeason"
                  :options="seasonOptions"
                  placeholder="选择赛季"
                  style="width: 100px"
                  size="small"
                  @update:value="onSeasonChange"
                />
                <n-button
                  v-if="!editMode"
                  @click="enterEditMode"
                  type="primary"
                  size="small"
                  :loading="verifyingPassword"
                >
                  编辑模式
                </n-button>
                <n-button v-else @click="exitEditMode" type="default" size="small">
                  退出编辑
                </n-button>
              </n-space>
            </div>
          </div>
        </template>

        <!-- 筛选控制区 -->
        <div class="filter-section" v-if="!loading">
          <n-space :wrap="true" class="filter-controls">
            <n-input
              v-model:value="searchKeyword"
              placeholder="搜索事件..."
              clearable
              style="width: 200px"
            >
              <template #prefix>
                <n-icon>
                  <SearchIcon />
                </n-icon>
              </template>
            </n-input>

            <n-select
              v-model:value="filterEventType"
              :options="eventTypeOptions"
              placeholder="事件类型"
              clearable
              style="width: 120px"
            />

            <n-select
              v-model:value="filterImportanceLevel"
              :options="importanceLevelOptions"
              placeholder="重要程度"
              clearable
              style="width: 100px"
            />

            <n-button
              v-if="editMode"
              @click="handleAddEvent"
              type="primary"
              ghost
              size="small"
            >
              <template #icon>
                <n-icon>
                  <AddIcon />
                </n-icon>
              </template>
              新增事件
            </n-button>

            <n-button
              v-if="editMode"
              @click="showEventTypeManager = true"
              type="info"
              ghost
              size="small"
            >
              <template #icon>
                <n-icon>
                  <SettingsIcon />
                </n-icon>
              </template>
              管理类型
            </n-button>
          </n-space>
        </div>
      </n-card>
    </div>

    <!-- 时间轴展示区 -->
    <div class="container">
      <n-card class="timeline-section" :bordered="false">
        <n-spin :show="loading" description="加载编年史数据中...">
          <div v-if="filteredEvents.length > 0" class="timeline-container">
            <HorizontalTimeline
              :events="filteredEvents"
              :edit-mode="editMode"
              @edit="handleEditEvent"
              @delete="handleDeleteEvent"
            />
          </div>

          <n-empty
            v-else-if="!loading"
            description="暂无编年史数据"
            style="margin: 40px 0"
          >
            <template #extra>
              <n-button
                v-if="editMode"
                @click="handleAddEvent"
                type="primary"
                ghost
              >
                立即添加第一个事件
              </n-button>
            </template>
          </n-empty>
        </n-spin>

        <!-- 错误提示 -->
        <n-alert
          v-if="error"
          type="error"
          :title="error"
          closable
          @close="clearError"
          style="margin-top: 16px"
        />
      </n-card>
    </div>

    <!-- 弹窗组件 -->
    <PasswordDialog
      v-model:show="showPasswordDialog"
      :saved-password="currentPassword"
      @success="onPasswordSuccess"
    />

    <EventEditor
      v-model:show="showEventEditor"
      :event="currentEvent"
      :season="currentSeason"
      :saved-password="currentPassword"
      @save="onEventSave"
    />

    <EventTypeManager
      v-model:show="showEventTypeManager"
      :password="currentPassword"
      @refresh="onEventTypeRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { HistoryIcon, SearchIcon, AddIcon, SettingsIcon } from '@/components/icons'
import * as economicsHistoryApi from '@/api/economicsHistory'
import type { EconomicsHistoryEvent } from '@/types/economicsHistory'
import {
  SEASON_OPTIONS,
  IMPORTANCE_LEVEL_OPTIONS,
  type EventType
} from '@/types/economicsHistory'

import HorizontalTimeline from './components/HorizontalTimeline.vue'
import EventEditor from './components/EventEditor.vue'
import EventTypeManager from './components/EventTypeManager.vue'
import PasswordDialog from './components/PasswordDialog.vue'
import { Storage } from '@/utils/storage'

const message = useMessage()
const dialog = useDialog()

// 响应式状态
const loading = ref(false)
const verifyingPassword = ref(false)
const error = ref<string | null>(null)
const events = ref<EconomicsHistoryEvent[]>([])
const editMode = ref(false)
const currentSeason = ref('S5')
const searchKeyword = ref('')
const filterEventType = ref('')
const filterImportanceLevel = ref<number | null>(null)
const showPasswordDialog = ref(false)
const showEventEditor = ref(false)
const showEventTypeManager = ref(false)
const currentEvent = ref<EconomicsHistoryEvent | null>(null)
const currentPassword = ref('')
const eventTypes = ref<EventType[]>([])

// 选项配置
const seasonOptions = SEASON_OPTIONS
const importanceLevelOptions = IMPORTANCE_LEVEL_OPTIONS

// 动态事件类型选项
const eventTypeOptions = computed(() => {
  if (!eventTypes.value?.length) return []
  
  return eventTypes.value
    .filter(type => type && type.status !== 0)
    .map(type => ({
      label: type.type_display_name || '未知类型',
      value: type.type_name || '',
    }))
})

// 计算属性 - 过滤后的事件列表
const filteredEvents = computed(() => {
  if (!events.value?.length) return []

  let result = [...events.value]

  // 搜索关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(event => 
      event.title?.toLowerCase().includes(keyword) ||
      event.description?.toLowerCase().includes(keyword) ||
      event.tags?.some(tag => tag?.toLowerCase().includes(keyword))
    )
  }

  // 事件类型过滤
  if (filterEventType.value) {
    result = result.filter(event => {
      const eventTypeName = event.event_type?.type_name || event.event_type_custom
      return eventTypeName === filterEventType.value
    })
  }

  // 重要程度过滤
  if (filterImportanceLevel.value !== null) {
    result = result.filter(event => 
      event.importance_level >= filterImportanceLevel.value!
    )
  }

  // 按日期升序排列
  return result.sort((a, b) => {
    const dateA = new Date(a.event_date + ' ' + (a.event_time || '00:00:00'))
    const dateB = new Date(b.event_date + ' ' + (b.event_time || '00:00:00'))
    return dateA.getTime() - dateB.getTime()
  })
})

// 方法
const loadSeasonEvents = async (season: string, skipCache = false) => {
  loading.value = true
  error.value = null

  try {
    const response = await economicsHistoryApi.getSeasonHistory(season, skipCache)
    if (response.code === 1 && response.data) {
      events.value = Array.isArray(response.data) ? response.data : []
    } else {
      throw new Error(response.msg || '获取数据失败')
    }
  } catch (err: any) {
    error.value = err.message || '加载编年史失败'
    console.error('加载失败:', err)
    events.value = []
  } finally {
    loading.value = false
  }
}

const loadEventTypes = async () => {
  try {
    const response = await economicsHistoryApi.getEventTypeList()
    if (response.code === 1 && response.data) {
      eventTypes.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (error) {
    console.error('加载事件类型失败:', error)
    eventTypes.value = []
  }
}

const onSeasonChange = (season: string) => {
  currentSeason.value = season
  loadSeasonEvents(season)
}

const enterEditMode = () => {
  const savedPassword = Storage.getEditCredentials()
  if (savedPassword) {
    currentPassword.value = savedPassword
    editMode.value = true
    message.success('已使用保存的密码进入编辑模式')
  } else {
    showPasswordDialog.value = true
  }
}

const exitEditMode = () => {
  editMode.value = false
  currentPassword.value = ''
  message.info('已退出编辑模式')
}

const handleEditEvent = (event: EconomicsHistoryEvent) => {
  currentEvent.value = event
  showEventEditor.value = true
}

const handleDeleteEvent = (event: EconomicsHistoryEvent) => {
  if (!currentPassword.value) {
    message.error('请先通过密码验证进入编辑模式')
    return
  }

  dialog.warning({
    title: '删除确认',
    content: `确定要删除事件："${event.title}" 吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const response = await economicsHistoryApi.deleteEvent({
          id: event.id,
          password: currentPassword.value
        })
        
        if (response.code === 1) {
          message.success('删除成功')
          await loadSeasonEvents(currentSeason.value, true)
        } else {
          throw new Error(response.msg || '删除失败')
        }
      } catch (error: any) {
        console.error('删除失败:', error)
        message.error(error.message || '删除失败')
      }
    }
  })
}

const handleAddEvent = () => {
  currentEvent.value = null
  showEventEditor.value = true
}

const onPasswordSuccess = (password: string) => {
  showPasswordDialog.value = false
  editMode.value = true
  currentPassword.value = password
  Storage.saveEditCredentials(password)
  message.success('验证成功，已进入编辑模式')
}

const onEventSave = async () => {
  showEventEditor.value = false
  await loadSeasonEvents(currentSeason.value, true)
}

const onEventTypeRefresh = async () => {
  await loadEventTypes()
  await loadSeasonEvents(currentSeason.value, true)
}

const clearError = () => {
  error.value = null
}

// 生命周期
onMounted(() => {
  loadEventTypes()
  loadSeasonEvents(currentSeason.value)
})
</script>

<style scoped>
.economics-history-page {
  width: 100%;
  padding: 0;
  margin: 0;
  background: var(--n-body-color);
  color: var(--n-text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
  width: 100%;
  padding: 0 12px;
}

.page-header {
  margin-bottom: 12px;
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24px;
  width: 100%;
  min-height: 60px;
}

.title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: var(--n-color-primary);
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.title-section h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--n-text-color);
  white-space: nowrap;
  transition: color 0.3s ease;
}

.subtitle {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 0.875rem;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.control-section {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.filter-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--n-border-color);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.timeline-section {
  min-height: 400px;
}

.timeline-container {
  padding: 20px 0;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .control-section {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 8px;
  }

  .filter-controls {
    gap: 8px;
  }

  .filter-controls > * {
    flex: 1 1 auto;
    min-width: 120px;
  }

  .title-section h1 {
    font-size: 1.25rem;
  }

  .control-section .n-space {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* 暗黑主题优化 */
html.dark .economics-history-page {
  --n-text-color-1: var(--n-text-color);
  --n-text-color-2: var(--n-text-color-2);
  --n-text-color-3: var(--n-text-color-3);
}

html.dark .economics-history-page :deep(.n-card) {
  background-color: var(--n-card-color) !important;
  border-color: var(--n-border-color) !important;
  color: var(--n-text-color) !important;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}
</style>
