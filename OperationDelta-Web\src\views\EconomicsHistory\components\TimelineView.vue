<template>
  <div class="timeline-view">
    <!-- 时间轴控制栏 -->
    <div class="timeline-controls" :class="{ 'edit-mode': editMode }">
      <div class="controls-left">
        <h3 class="timeline-title">
          <n-icon v-if="editMode" :component="EditIcon" />
          <n-icon v-else :component="TimeIcon" />
          经济历史时间轴
        </h3>
      </div>
      <div class="controls-right">
        <n-space>
          <n-button-group size="small">
            <n-button @click="zoomIn" ghost>
              <template #icon>
                <n-icon :component="ZoomInIcon" />
              </template>
            </n-button>
            <n-button @click="resetZoom" ghost>
              <template #icon>
                <n-icon :component="FullscreenIcon" />
              </template>
            </n-button>
            <n-button @click="zoomOut" ghost>
              <template #icon>
                <n-icon :component="ZoomOutIcon" />
              </template>
            </n-button>
          </n-button-group>
          <n-text depth="3" style="font-size: 12px;">
            事件总数: {{ events.length }}
          </n-text>
        </n-space>
      </div>
    </div>

    <!-- 时间轴容器 -->
    <div class="timeline-container">
      <div ref="timelineRef" class="timeline-element"></div>
      <div v-if="events.length > 3" class="scroll-hint">
        <n-text depth="3" style="font-size: 12px;">
          ← 左右滑动查看更多事件 →
        </n-text>
      </div>
    </div>

    <!-- 事件详情弹窗 -->
    <n-modal
      v-model:show="showDetailModal"
      preset="card"
      :style="{ maxWidth: '600px' }"
      title="事件详情"
      size="huge"
      :bordered="false"
      :segmented="false"
    >
      <div v-if="selectedEvent" class="event-detail">
        <div class="detail-header">
          <h2>{{ selectedEvent.title }}</h2>
          <div class="detail-meta">
            <n-tag :type="getEventTypeTagType(selectedEvent)" size="small">
              {{ getEventTypeDisplayName(selectedEvent) }}
            </n-tag>
            <span class="detail-date">{{ formatDate(selectedEvent.event_date) }}</span>
            <div class="detail-importance">
              <span class="importance-label">重要程度:</span>
              <n-rate 
                :value="selectedEvent.importance_level" 
                readonly 
                size="small"
                :count="5"
              />
            </div>
          </div>
        </div>
        
        <div v-if="selectedEvent.description" class="detail-description">
          <h4>详细描述</h4>
          <p>{{ selectedEvent.description }}</p>
        </div>
        
        <div v-if="selectedEvent.tags && selectedEvent.tags.length > 0" class="detail-tags">
          <h4>相关标签</h4>
          <div class="tags-container">
            <n-tag 
              v-for="tag in selectedEvent.tags" 
              :key="tag" 
              size="small"
              type="info"
            >
              {{ tag }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { NIcon, NButton, NButtonGroup, NSpace, NText, NModal, NTag, NRate, useMessage } from 'naive-ui'
import {
  TimeOutline as TimeIcon,
  CreateOutline as EditIcon,
  AddOutline as ZoomInIcon,
  RemoveOutline as ZoomOutIcon,
  ExpandOutline as FullscreenIcon
} from '@vicons/ionicons5'
import type { EconomicsHistoryEvent } from '@/types/economicsHistory'
import { getEventDisplayType, getEventTypeColor } from '@/types/economicsHistory'

// Props 定义
interface Props {
  events: EconomicsHistoryEvent[]
  editMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editMode: false
})

// Emits 定义
const emit = defineEmits<{
  edit: [event: EconomicsHistoryEvent]
  delete: [event: EconomicsHistoryEvent]
}>()

// 响应式数据
const timelineRef = ref<HTMLDivElement>()
const showDetailModal = ref(false)
const selectedEvent = ref<EconomicsHistoryEvent | null>(null)
const timeline = ref<any>(null)

// 消息提示
const message = useMessage()

// 工具函数
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const getEventTypeDisplayName = (event: EconomicsHistoryEvent) => {
  return getEventDisplayType(event)
}

const getEventTypeTagType = (event: EconomicsHistoryEvent): 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' => {
  const typeName = event.event_type?.type_name || event.event_type_custom || 'default'
  
  const typeMapping: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary'> = {
    'monetary_policy': 'info',
    'fiscal_policy': 'success',
    'market_event': 'warning',
    'regulatory_change': 'error',
    'economic_indicator': 'default',
    'international_event': 'primary',
    'season_start': 'success',
    'update': 'info',
    'market': 'warning',
    'policy': 'primary',
    'bug_fix': 'error',
    'price_adjustment': 'warning',
    'new_item': 'info',
    'balance_change': 'success',
    'major_event': 'error'
  }
  
  return typeMapping[typeName] || 'default'
}

// 创建横向时间轴
const createSimpleTimeline = () => {
  if (!timelineRef.value || !props.events.length) return

  const container = timelineRef.value
  container.innerHTML = '' // 清空容器

  // 创建横向时间轴容器
  const timelineContainer = document.createElement('div')
  timelineContainer.className = 'horizontal-timeline'

  // 按日期排序事件
  const sortedEvents = [...props.events].sort((a, b) =>
    new Date(a.event_date).getTime() - new Date(b.event_date).getTime()
  )

  // 创建时间轴主线
  const timelineLine = document.createElement('div')
  timelineLine.className = 'timeline-line'
  timelineContainer.appendChild(timelineLine)

  // 创建事件项
  sortedEvents.forEach((event, index) => {
    const eventItem = document.createElement('div')
    eventItem.className = 'timeline-item'

    // 计算位置，确保首尾事件不会超出边界
    const totalEvents = sortedEvents.length
    let leftPercent
    if (totalEvents === 1) {
      leftPercent = 50 // 单个事件居中
    } else {
      // 为首尾事件留出空间，避免卡片超出容器
      const margin = 15 // 15% 的边距
      leftPercent = margin + (index / (totalEvents - 1)) * (100 - 2 * margin)
    }
    eventItem.style.left = `${leftPercent}%`

    // 创建时间点
    const timePoint = document.createElement('div')
    timePoint.className = 'timeline-point'
    timePoint.style.backgroundColor = getEventTypeColor(event)

    // 创建事件卡片
    const eventCard = document.createElement('div')
    eventCard.className = `timeline-card ${index % 2 === 0 ? 'card-above' : 'card-below'}`

    // 连接线
    const connector = document.createElement('div')
    connector.className = 'timeline-connector'
    connector.style.backgroundColor = getEventTypeColor(event)

    // 事件日期
    const eventDate = document.createElement('div')
    eventDate.className = 'event-date'
    eventDate.textContent = formatDate(event.event_date)

    // 事件标题
    const eventTitle = document.createElement('h4')
    eventTitle.className = 'event-title'
    eventTitle.textContent = event.title

    // 事件类型
    const eventType = document.createElement('span')
    eventType.className = 'event-type'
    eventType.textContent = getEventTypeDisplayName(event)
    eventType.style.color = getEventTypeColor(event)

    // 事件描述
    const eventDescription = document.createElement('p')
    eventDescription.className = 'event-description'
    eventDescription.textContent = event.description ?
      (event.description.length > 60 ? event.description.slice(0, 60) + '...' : event.description) : ''

    // 重要程度星标
    if (event.importance_level > 0) {
      const stars = document.createElement('div')
      stars.className = 'event-stars'
      stars.innerHTML = '★'.repeat(event.importance_level) + '☆'.repeat(5 - event.importance_level)
      eventCard.appendChild(stars)
    }

    eventCard.appendChild(eventDate)
    eventCard.appendChild(eventTitle)
    eventCard.appendChild(eventType)
    if (event.description) {
      eventCard.appendChild(eventDescription)
    }

    // 编辑模式下的操作按钮
    if (props.editMode) {
      const actions = document.createElement('div')
      actions.className = 'event-actions'

      const editBtn = document.createElement('button')
      editBtn.textContent = '编辑'
      editBtn.className = 'action-btn edit-btn'
      editBtn.onclick = (e) => {
        e.stopPropagation()
        emit('edit', event)
      }

      const deleteBtn = document.createElement('button')
      deleteBtn.textContent = '删除'
      deleteBtn.className = 'action-btn delete-btn'
      deleteBtn.onclick = (e) => {
        e.stopPropagation()
        emit('delete', event)
      }

      actions.appendChild(editBtn)
      actions.appendChild(deleteBtn)
      eventCard.appendChild(actions)
    }

    // 点击事件
    eventCard.style.cursor = 'pointer'
    eventCard.onclick = () => {
      if (!props.editMode) {
        selectedEvent.value = event
        showDetailModal.value = true
      }
    }

    // 组装事件项
    eventItem.appendChild(timePoint)
    eventItem.appendChild(connector)
    eventItem.appendChild(eventCard)
    timelineContainer.appendChild(eventItem)
  })

  container.appendChild(timelineContainer)
}

// 缩放控制
const zoomIn = () => {
  message.info('放大时间轴')
  // 简单实现：调整字体大小
  if (timelineRef.value) {
    const currentSize = parseFloat(getComputedStyle(timelineRef.value).fontSize) || 14
    timelineRef.value.style.fontSize = Math.min(currentSize * 1.1, 20) + 'px'
  }
}

const zoomOut = () => {
  message.info('缩小时间轴')
  if (timelineRef.value) {
    const currentSize = parseFloat(getComputedStyle(timelineRef.value).fontSize) || 14
    timelineRef.value.style.fontSize = Math.max(currentSize * 0.9, 10) + 'px'
  }
}

const resetZoom = () => {
  message.info('重置缩放')
  if (timelineRef.value) {
    timelineRef.value.style.fontSize = '14px'
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    createSimpleTimeline()
  })
})

onUnmounted(() => {
  if (timeline.value) {
    timeline.value = null
  }
})

// 监听数据变化
watch(() => props.events, () => {
  nextTick(() => {
    createSimpleTimeline()
  })
}, { deep: true })
</script>

<style scoped>
.timeline-view {
  width: 100%;
  background: var(--n-card-color);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--n-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.timeline-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--n-border-color);
  transition: all 0.3s ease;
}

.timeline-controls.edit-mode {
  background: var(--n-warning-color-supressed, #fff7e6);
  border-left: 4px solid var(--n-warning-color);
}

.timeline-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
  transition: color 0.3s ease;
}

.timeline-container {
  width: 100%;
  min-height: 450px;
  padding: 20px;
  overflow-x: auto;
  overflow-y: visible;
  position: relative;
}

.timeline-element {
  width: 100%;
  height: 100%;
  font-size: 14px;
  transition: font-size 0.3s ease;
}

.scroll-hint {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  opacity: 0.6;
  animation: fadeInOut 3s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* 横向时间轴样式 */
.horizontal-timeline {
  position: relative;
  width: 100%;
  height: 400px;
  padding: 40px 20px;
  overflow-x: auto;
  overflow-y: visible;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 20px;
  right: 20px;
  height: 3px;
  background: linear-gradient(90deg, var(--n-color-primary), var(--n-color-primary-hover));
  border-radius: 2px;
  transform: translateY(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-item {
  position: absolute;
  top: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.timeline-point {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 3px solid var(--n-card-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-point:hover {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.timeline-connector {
  position: absolute;
  width: 2px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.timeline-card {
  position: absolute;
  width: 280px;
  background: var(--n-card-color);
  border: 1px solid var(--n-border-color);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  left: 50%;
  transform: translateX(-50%);
}

.timeline-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateX(-50%) translateY(-4px);
}

.card-above {
  bottom: 40px;
}

.card-above .timeline-connector {
  top: 8px;
  height: 32px;
}

.card-below {
  top: 40px;
}

.card-below .timeline-connector {
  bottom: 8px;
  height: 32px;
}

.event-date {
  font-size: 11px;
  color: var(--n-color-primary);
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
  padding: 4px 8px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 12px;
}

.event-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color);
  line-height: 1.4;
  text-align: center;
}

.event-type {
  display: block;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 8px;
  opacity: 0.8;
}

.event-description {
  margin: 8px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
}

.event-stars {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #ffd700;
  font-size: 10px;
  letter-spacing: 1px;
}

.event-actions {
  display: flex;
  gap: 6px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--n-border-color);
  justify-content: center;
}

.action-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-btn {
  background: var(--n-color-primary);
  color: white;
}

.edit-btn:hover {
  background: var(--n-color-primary-hover);
}

.delete-btn {
  background: var(--n-error-color);
  color: white;
}

.delete-btn:hover {
  background: var(--n-error-color-hover);
}

/* 事件详情样式 */
.event-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-header {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.detail-date {
  font-size: 14px;
  color: var(--n-text-color-2);
  font-weight: 500;
}

.detail-importance {
  display: flex;
  align-items: center;
  gap: 8px;
}

.importance-label {
  font-size: 13px;
  color: var(--n-text-color-3);
}

.detail-description h4,
.detail-tags h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.detail-description p {
  margin: 0;
  line-height: 1.6;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.tags-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式 */
@media (max-width: 768px) {
  .timeline-controls {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .timeline-container {
    padding: 16px;
  }

  .horizontal-timeline {
    height: 350px;
    padding: 30px 15px;
  }

  .timeline-card {
    width: 220px;
  }

  .event-title {
    font-size: 13px;
  }

  .event-description {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .timeline-container {
    padding: 12px;
  }

  .timeline-controls {
    padding: 10px 12px;
  }

  .timeline-title {
    font-size: 14px;
  }

  .horizontal-timeline {
    height: 300px;
    padding: 25px 10px;
  }

  .timeline-card {
    width: 180px;
    padding: 12px;
  }

  .event-title {
    font-size: 12px;
  }

  .event-description {
    font-size: 10px;
    display: none; /* 在小屏幕上隐藏描述 */
  }

  .event-actions {
    gap: 4px;
  }

  .action-btn {
    padding: 2px 8px;
    font-size: 10px;
  }
}

/* 暗黑主题优化 */
html.dark .timeline-view {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

html.dark .timeline-controls.edit-mode {
  background: rgba(250, 173, 20, 0.1);
  border-left-color: var(--n-warning-color);
}

html.dark .timeline-content {
  background-color: var(--n-card-color) !important;
  border-color: var(--n-border-color) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

html.dark .timeline-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style>
