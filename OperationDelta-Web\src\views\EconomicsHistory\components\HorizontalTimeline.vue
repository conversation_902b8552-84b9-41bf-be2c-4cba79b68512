<template>
  <div class="horizontal-timeline-wrapper">
    <!-- 时间轴控制栏 -->
    <div class="timeline-controls" :class="{ 'edit-mode': editMode }">
      <div class="controls-left">
        <h3 class="timeline-title">
          <n-icon v-if="editMode" :component="EditIcon" />
          <n-icon v-else :component="TimeIcon" />
          经济历史时间轴
        </h3>
        <n-text depth="3" style="font-size: 12px; margin-left: 8px;">
          共 {{ events.length }} 个事件
        </n-text>
      </div>
      <div class="controls-right">
        <n-space>
          <n-button-group size="small">
            <n-button @click="scrollToStart" ghost>
              <template #icon>
                <n-icon :component="ChevronLeftIcon" />
              </template>
              开始
            </n-button>
            <n-button @click="scrollToEnd" ghost>
              <template #icon>
                <n-icon :component="ChevronRightIcon" />
              </template>
              最新
            </n-button>
          </n-button-group>
        </n-space>
      </div>
    </div>

    <!-- 横向时间轴主体 -->
    <div class="timeline-container" ref="timelineContainer">
      <div class="timeline-track" ref="timelineTrack">
        <!-- 时间轴主线 -->
        <div class="timeline-line"></div>
        
        <!-- 事件节点 -->
        <div
          v-for="(event, index) in sortedEvents"
          :key="event.id || index"
          class="timeline-event"
          :style="getEventPosition(index)"
        >
          <!-- 时间点 -->
          <div 
            class="timeline-point"
            :style="{ backgroundColor: getEventTypeColor(event) }"
            @click="handleEventClick(event)"
          >
            <div class="point-inner"></div>
          </div>

          <!-- 连接线 -->
          <div 
            class="timeline-connector"
            :class="{ 'connector-above': index % 2 === 0, 'connector-below': index % 2 === 1 }"
            :style="{ backgroundColor: getEventTypeColor(event) }"
          ></div>

          <!-- 事件卡片 -->
          <div
            class="timeline-card"
            :class="{
              'card-above': index % 2 === 0,
              'card-below': index % 2 === 1,
              'card-edit-mode': editMode
            }"
            :style="getCardStyle(event)"
            @click="handleEventClick(event)"
          >

            <!-- 事件日期 -->
            <div class="event-date">
              {{ formatDate(event.event_date) }}
              <span v-if="event.event_time" class="event-time">
                {{ formatTime(event.event_time) }}
              </span>
            </div>

            <!-- 事件标题 -->
            <h4 class="event-title">{{ event.title }}</h4>

            <!-- 事件类型 -->
            <div class="event-type" :style="{ color: getEventTypeColor(event) }">
              {{ getEventTypeDisplayName(event) }}
            </div>

            <!-- 事件描述 -->
            <p v-if="event.description" class="event-description">
              {{ truncateText(event.description, 60) }}
            </p>

            <!-- 标签 -->
            <div v-if="event.tags && event.tags.length > 0" class="event-tags">
              <n-tag
                v-for="tag in event.tags.slice(0, 3)"
                :key="tag"
                size="small"
                type="info"
                class="event-tag"
              >
                {{ tag }}
              </n-tag>
              <span v-if="event.tags.length > 3" class="more-tags">
                +{{ event.tags.length - 3 }}
              </span>
            </div>

            <!-- 重要程度星级（小型辅助显示） -->
            <div v-if="event.importance_level > 0" class="importance-stars">
              <span
                v-for="i in 5"
                :key="i"
                class="star"
                :class="{ 'star-filled': i <= event.importance_level }"
              >
                ★
              </span>
            </div>

            <!-- 编辑模式操作按钮 -->
            <div v-if="editMode" class="event-actions">
              <n-button
                size="tiny"
                type="primary"
                ghost
                @click.stop="$emit('edit', event)"
              >
                编辑
              </n-button>
              <n-button
                size="tiny"
                type="error"
                ghost
                @click.stop="$emit('delete', event)"
              >
                删除
              </n-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 滚动提示 -->
      <div v-if="events.length > 4" class="scroll-hint">
        <n-text depth="3" style="font-size: 11px;">
          ← 拖拽或使用鼠标滚轮横向浏览 →
        </n-text>
      </div>
    </div>

    <!-- 事件详情弹窗 -->
    <n-modal
      v-model:show="showDetailModal"
      preset="card"
      :style="{ maxWidth: '600px' }"
      title="事件详情"
      size="huge"
      :bordered="false"
      :segmented="false"
    >
      <div v-if="selectedEvent" class="event-detail">
        <div class="detail-header">
          <h2>{{ selectedEvent.title }}</h2>
          <div class="detail-meta">
            <n-tag :type="getEventTypeTagType(selectedEvent)" size="small">
              {{ getEventTypeDisplayName(selectedEvent) }}
            </n-tag>
            <span class="detail-date">{{ formatDate(selectedEvent.event_date) }}</span>
            <div class="detail-importance">
              <span class="importance-label">重要程度:</span>
              <n-rate 
                :value="selectedEvent.importance_level" 
                readonly 
                size="small"
                :count="5"
              />
            </div>
          </div>
        </div>
        
        <div v-if="selectedEvent.description" class="detail-description">
          <h4>详细描述</h4>
          <p>{{ selectedEvent.description }}</p>
        </div>
        
        <div v-if="selectedEvent.tags && selectedEvent.tags.length > 0" class="detail-tags">
          <h4>相关标签</h4>
          <div class="tags-container">
            <n-tag 
              v-for="tag in selectedEvent.tags" 
              :key="tag" 
              size="small"
              type="info"
            >
              {{ tag }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import { NIcon, NButton, NButtonGroup, NSpace, NText, NModal, NTag, NRate } from 'naive-ui'
import {
  TimeOutline as TimeIcon,
  CreateOutline as EditIcon,
  ChevronBackOutline as ChevronLeftIcon,
  ChevronForwardOutline as ChevronRightIcon
} from '@vicons/ionicons5'
import type { EconomicsHistoryEvent } from '@/types/economicsHistory'
import { getEventDisplayType, getEventTypeColor } from '@/types/economicsHistory'

// Props 定义
interface Props {
  events: EconomicsHistoryEvent[]
  editMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editMode: false
})

// Emits 定义
const emit = defineEmits<{
  edit: [event: EconomicsHistoryEvent]
  delete: [event: EconomicsHistoryEvent]
}>()

// 响应式数据
const timelineContainer = ref<HTMLDivElement>()
const timelineTrack = ref<HTMLDivElement>()
const showDetailModal = ref(false)
const selectedEvent = ref<EconomicsHistoryEvent | null>(null)

// 计算属性
const sortedEvents = computed(() => {
  if (!props.events?.length) return []
  
  return [...props.events]
    .filter(event => event && event.event_date)
    .sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())
})

// 工具函数
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatTime = (timeStr: string) => {
  return timeStr.slice(0, 5) // HH:MM
}

const truncateText = (text: string, maxLength: number) => {
  if (!text) return ''
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text
}

const getEventTypeDisplayName = (event: EconomicsHistoryEvent) => {
  return getEventDisplayType(event)
}

const getEventTypeTagType = (event: EconomicsHistoryEvent): 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' => {
  const typeName = event.event_type?.type_name || event.event_type_custom || 'default'

  const typeMapping: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary'> = {
    'monetary_policy': 'info',
    'fiscal_policy': 'success',
    'market_event': 'warning',
    'regulatory_change': 'error',
    'economic_indicator': 'default',
    'international_event': 'primary',
    'season_start': 'success',
    'update': 'info',
    'market': 'warning',
    'policy': 'primary',
    'bug_fix': 'error',
    'price_adjustment': 'warning',
    'new_item': 'info',
    'balance_change': 'success',
    'major_event': 'error'
  }

  return typeMapping[typeName] || 'default'
}

// 根据重要程度获取卡片样式
const getCardStyle = (event: EconomicsHistoryEvent) => {
  const importance = Math.max(1, Math.min(5, event.importance_level || 1))
  const baseColor = getEventTypeColor(event)

  // 根据重要程度设置边框宽度和阴影
  const styles = {
    1: { borderWidth: '1px', shadowSize: '0px', shadowOpacity: '0.1' },
    2: { borderWidth: '2px', shadowSize: '1px', shadowOpacity: '0.2' },
    3: { borderWidth: '2px', shadowSize: '2px', shadowOpacity: '0.3' },
    4: { borderWidth: '3px', shadowSize: '3px', shadowOpacity: '0.4' },
    5: { borderWidth: '3px', shadowSize: '4px', shadowOpacity: '0.5' }
  }

  const style = styles[importance as keyof typeof styles]

  // 将十六进制颜色转换为 RGB 以便添加透明度
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 24, g: 144, b: 255 } // 默认蓝色
  }

  const rgb = hexToRgb(baseColor)
  const shadowColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${style.shadowOpacity})`

  return {
    borderColor: baseColor,
    borderWidth: style.borderWidth,
    boxShadow: `0 6px 18px rgba(0, 0, 0, 0.08), 0 0 0 ${style.shadowSize} ${shadowColor}`
  }
}

// 获取事件位置 - 防止重叠的智能布局
const getEventPosition = (index: number) => {
  const totalEvents = sortedEvents.value.length
  if (totalEvents <= 1) return { left: '50%' }

  // 计算最小间距，确保卡片不重叠
  const cardWidth = 240 // 卡片宽度
  const containerWidth = 800 // 最小容器宽度
  const minSpacing = cardWidth * 0.8 // 最小间距为卡片宽度的80%

  // 计算需要的总宽度
  const requiredWidth = Math.max(containerWidth, totalEvents * minSpacing + 200)

  // 动态设置轨道宽度
  nextTick(() => {
    if (timelineTrack.value) {
      timelineTrack.value.style.minWidth = `${requiredWidth}px`
    }
  })

  // 计算位置百分比
  const margin = 120 // 固定边距（像素）
  const availableWidth = requiredWidth - 2 * margin
  const leftPixels = margin + (index / (totalEvents - 1)) * availableWidth
  const leftPercent = (leftPixels / requiredWidth) * 100

  return { left: `${leftPercent}%` }
}

// 事件处理
const handleEventClick = (event: EconomicsHistoryEvent) => {
  if (props.editMode) return
  
  selectedEvent.value = event
  showDetailModal.value = true
}

const scrollToStart = () => {
  if (timelineContainer.value) {
    timelineContainer.value.scrollTo({ left: 0, behavior: 'smooth' })
  }
}

const scrollToEnd = () => {
  if (timelineContainer.value) {
    timelineContainer.value.scrollTo({ 
      left: timelineContainer.value.scrollWidth, 
      behavior: 'smooth' 
    })
  }
}

// 生命周期
onMounted(() => {
  // 启用横向滚动
  if (timelineContainer.value) {
    timelineContainer.value.addEventListener('wheel', (e) => {
      if (e.deltaY !== 0) {
        e.preventDefault()
        timelineContainer.value!.scrollLeft += e.deltaY
      }
    })
  }
})
</script>

<style scoped>
.horizontal-timeline-wrapper {
  width: 100%;
  background: var(--n-card-color);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--n-border-color);
  transition: all 0.3s ease;
}

.timeline-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--n-border-color);
  background: var(--n-card-color);
  transition: all 0.3s ease;
}

.timeline-controls.edit-mode {
  background: linear-gradient(135deg, var(--n-warning-color-supressed, #fff7e6), var(--n-card-color));
  border-left: 4px solid var(--n-warning-color);
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timeline-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
  transition: color 0.3s ease;
}

.timeline-container {
  position: relative;
  width: 100%;
  height: 480px;
  overflow-x: auto;
  overflow-y: visible;
  padding: 40px 0;
  background: linear-gradient(180deg, var(--n-card-color) 0%, rgba(24, 144, 255, 0.02) 50%, var(--n-card-color) 100%);
}

.timeline-track {
  position: relative;
  width: 100%;
  min-width: 1000px;
  height: 100%;
  padding: 0 60px;
  transition: min-width 0.3s ease;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 60px;
  right: 60px;
  height: 4px;
  background: linear-gradient(90deg,
    var(--n-color-primary-hover),
    var(--n-color-primary),
    var(--n-color-primary-hover)
  );
  border-radius: 2px;
  transform: translateY(-50%);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.timeline-event {
  position: absolute;
  top: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.timeline-point {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 4px solid var(--n-card-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.timeline-point:hover {
  transform: translate(-50%, -50%) scale(1.3);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.point-inner {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--n-card-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.timeline-connector {
  position: absolute;
  width: 3px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  border-radius: 2px;
  opacity: 0.8;
}

.connector-above {
  bottom: 10px;
  height: 40px;
}

.connector-below {
  top: 10px;
  height: 40px;
}

.timeline-card {
  position: absolute;
  width: 240px;
  background: var(--n-card-color);
  border: 1px solid var(--n-border-color);
  border-radius: 10px;
  padding: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  backdrop-filter: blur(10px);
  /* 重要程度边框会通过内联样式覆盖 */
}

.timeline-card:hover {
  transform: translateX(-50%) translateY(-6px);
  /* 悬停时的边框颜色和阴影会保持重要程度的设置 */
}

.card-above {
  bottom: 50px;
}

.card-below {
  top: 50px;
}

.card-edit-mode {
  border-color: var(--n-warning-color);
}

.card-edit-mode:hover {
  border-color: var(--n-warning-color-hover);
  box-shadow: 0 12px 32px rgba(250, 173, 20, 0.2);
}



.event-date {
  font-size: 11px;
  color: var(--n-color-primary);
  font-weight: 600;
  margin-bottom: 6px;
  text-align: center;
  padding: 3px 8px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.event-time {
  display: block;
  font-size: 9px;
  opacity: 0.8;
  margin-top: 1px;
}

.event-title {
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color);
  line-height: 1.3;
  text-align: center;
}

.event-type {
  display: block;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 6px;
  opacity: 0.9;
}

.event-description {
  margin: 6px 0;
  color: var(--n-text-color-2);
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
  /* 限制最多显示2行，超出省略 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: calc(1.4em * 2); /* 2行的高度 */
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin: 6px 0;
  justify-content: center;
}

.event-tag {
  font-size: 9px !important;
  padding: 2px 5px !important;
}

.more-tags {
  font-size: 9px;
  color: var(--n-text-color-3);
  align-self: center;
}

.importance-stars {
  display: flex;
  justify-content: center;
  gap: 1px;
  margin: 4px 0 0 0;
  font-size: 10px;
  line-height: 1;
}

.star {
  color: #e0e0e0;
  transition: color 0.2s ease;
}

.star-filled {
  color: #ffd700;
}

.event-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px solid var(--n-border-color);
  justify-content: center;
}

.scroll-hint {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  opacity: 0.6;
  animation: fadeInOut 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* 事件详情样式 */
.event-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-header {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.detail-date {
  font-size: 14px;
  color: var(--n-text-color-2);
  font-weight: 500;
}

.detail-importance {
  display: flex;
  align-items: center;
  gap: 8px;
}

.importance-label {
  font-size: 13px;
  color: var(--n-text-color-3);
}

.detail-description h4,
.detail-tags h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.detail-description p {
  margin: 0;
  line-height: 1.6;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.tags-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .timeline-card {
    width: 220px;
    padding: 10px;
  }

  .timeline-track {
    padding: 0 40px;
    min-width: 900px;
  }

  .timeline-line {
    left: 40px;
    right: 40px;
  }
}

@media (max-width: 768px) {
  .timeline-controls {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .timeline-container {
    height: 350px;
    padding: 25px 0;
  }

  .timeline-card {
    width: 200px;
    padding: 10px;
  }

  .event-title {
    font-size: 13px;
  }

  .event-description {
    font-size: 11px;
  }

  .event-tag {
    font-size: 8px !important;
    padding: 1px 4px !important;
  }

  .timeline-track {
    padding: 0 30px;
    min-width: 700px;
  }

  .timeline-line {
    left: 30px;
    right: 30px;
  }

  .connector-above,
  .connector-below {
    height: 35px;
  }

  .card-above {
    bottom: 45px;
  }

  .card-below {
    top: 45px;
  }
}

@media (max-width: 480px) {
  .timeline-container {
    height: 300px;
    padding: 20px 0;
  }

  .timeline-card {
    width: 160px;
    padding: 8px;
  }

  .event-title {
    font-size: 12px;
  }

  .event-description {
    display: none; /* 在小屏幕上隐藏描述 */
  }

  .event-tags {
    margin: 4px 0;
  }

  .event-tag {
    font-size: 8px !important;
    padding: 1px 3px !important;
  }

  .importance-stars {
    font-size: 9px;
    margin: 3px 0 0 0;
  }

  .timeline-track {
    padding: 0 20px;
    min-width: 600px;
  }

  .timeline-line {
    left: 20px;
    right: 20px;
  }

  .connector-above,
  .connector-below {
    height: 30px;
  }

  .card-above {
    bottom: 40px;
  }

  .card-below {
    top: 40px;
  }
}

/* 暗黑主题优化 */
html.dark .horizontal-timeline-wrapper {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

html.dark .timeline-controls.edit-mode {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.1), var(--n-card-color));
  border-left-color: var(--n-warning-color);
}

html.dark .timeline-container {
  background: linear-gradient(180deg, var(--n-card-color) 0%, rgba(24, 144, 255, 0.05) 50%, var(--n-card-color) 100%);
}

html.dark .timeline-line {
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
}

html.dark .timeline-card {
  background-color: var(--n-card-color) !important;
  border-color: var(--n-border-color) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

html.dark .timeline-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
  border-color: var(--n-color-primary) !important;
}

html.dark .card-edit-mode {
  border-color: var(--n-warning-color) !important;
}

html.dark .card-edit-mode:hover {
  border-color: var(--n-warning-color-hover) !important;
  box-shadow: 0 12px 32px rgba(250, 173, 20, 0.3);
}

html.dark .timeline-point {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

html.dark .timeline-point:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}
</style>
