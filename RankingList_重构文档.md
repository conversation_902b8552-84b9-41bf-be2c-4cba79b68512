# RankingList.vue 表格重构文档

## 概述

本文档描述了如何将 `OperationDelta-Web/src/views/Ranking/components/SpecialOperation/RankingList.vue` 组件重构为更优化的表格形式。该组件目前已经是表格形式，但可以进一步优化其结构、性能和用户体验。

## 当前组件分析

### 组件结构
- **模板**: 使用 Naive UI 的 `n-data-table` 组件
- **数据**: 特勤处操作项列表 (`SpecialOperationItem[]`)
- **功能**: 排序、分页、点击事件、材料展示
- **样式**: 响应式设计，支持移动端适配

### 主要特性
1. **排名显示**: 前三名特殊样式（金银铜）
2. **物品信息**: 图片、名称、等级指示器
3. **价格数据**: 售价、成本、手续费、保证金、净利润
4. **生产信息**: 周期、数量、每小时利润
5. **材料展示**: 使用 MaterialsList 子组件
6. **交互功能**: 排序、分页、行点击

## 重构目标

### 1. 性能优化
- 虚拟滚动支持大数据量
- 懒加载材料信息
- 优化渲染函数

### 2. 用户体验改进
- 更好的加载状态
- 优化的移动端体验
- 增强的排序和筛选

### 3. 代码结构优化
- 组件拆分
- 类型安全增强
- 可维护性提升

## 重构方案

### 方案一：渐进式优化（推荐）

#### 1.1 表格列配置优化

```typescript
// 将列配置提取为独立的 composable
export function useTableColumns(props: Props, emit: EmitFunction) {
  const columns = computed<DataTableColumns<SpecialOperationItem & { rank: number }>>(() => [
    // 排名列 - 优化渲染
    {
      title: '排名',
      key: 'rank',
      width: 70,
      fixed: 'left',
      render: (row) => h(RankBadge, { rank: row.rank })
    },
    
    // 物品列 - 组件化
    {
      title: '物品',
      key: 'name',
      width: 200,
      fixed: 'left',
      render: (row) => h(ItemInfo, { 
        item: row,
        onClick: () => emit('item-click', row)
      })
    },
    
    // 价格相关列 - 统一格式化
    ...createPriceColumns(),
    
    // 生产信息列
    ...createProductionColumns(),
    
    // 材料列 - 懒加载优化
    {
      title: '所需材料',
      key: 'materials',
      width: 220,
      render: (row) => h(MaterialsList, {
        materials: row.materials,
        materialInfos: props.materialInfos,
        lazy: true
      })
    }
  ])
  
  return { columns }
}
```

#### 1.2 子组件拆分

**RankBadge.vue** - 排名徽章组件
```vue
<template>
  <span :class="rankClass">{{ rank }}</span>
</template>

<script setup lang="ts">
interface Props {
  rank: number
}

const props = defineProps<Props>()

const rankClass = computed(() => {
  if (props.rank <= 3) return `rank-badge rank-${props.rank}`
  return 'rank-badge rank-normal'
})
</script>
```

**ItemInfo.vue** - 物品信息组件
```vue
<template>
  <div class="item-info" @click="$emit('click')">
    <div :class="imageWrapperClass">
      <n-image
        :src="item.image || '/images/default-item.png'"
        :width="32"
        :height="32"
        class="item-image"
        :fallback-src="'/images/default-item.png'"
      />
      <div :class="gradeClass">{{ item.grade || 1 }}</div>
    </div>
    <div class="item-details">
      <div class="item-name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SpecialOperationItem } from '@/types/specialOperation'

interface Props {
  item: SpecialOperationItem
}

defineProps<Props>()
defineEmits<{
  click: []
}>()
</script>
```

#### 1.3 性能优化

**虚拟滚动支持**
```typescript
// 使用 Naive UI 的虚拟滚动
const tableProps = computed(() => ({
  columns: columns.value,
  data: tableData.value,
  loading: props.loading,
  pagination: paginationConfig.value,
  virtualScroll: props.items.length > 100, // 大数据量时启用
  maxHeight: 600,
  scrollX: 1200
}))
```

**懒加载材料信息**
```typescript
// 材料信息懒加载
const visibleMaterials = ref(new Set<string>())

const loadMaterialsForVisibleRows = async (startIndex: number, endIndex: number) => {
  const materialIds = new Set<string>()
  
  for (let i = startIndex; i <= endIndex; i++) {
    const item = props.items[i]
    if (item?.materials) {
      const materials = parseCustomMaterials(item.materials)
      Object.keys(materials).forEach(id => materialIds.add(id))
    }
  }
  
  // 批量加载材料信息
  const newIds = Array.from(materialIds).filter(id => !visibleMaterials.value.has(id))
  if (newIds.length > 0) {
    await loadBulkMaterialInfo(newIds)
    newIds.forEach(id => visibleMaterials.value.add(id))
  }
}
```

### 方案二：完全重构

#### 2.1 新的组件架构

```
RankingTable/
├── index.vue              # 主组件
├── components/
│   ├── TableHeader.vue    # 表头组件
│   ├── TableRow.vue       # 行组件
│   ├── RankBadge.vue      # 排名徽章
│   ├── ItemCell.vue       # 物品单元格
│   ├── PriceCell.vue      # 价格单元格
│   ├── MaterialsCell.vue  # 材料单元格
│   └── LoadingRow.vue     # 加载行
├── composables/
│   ├── useTableData.ts    # 表格数据处理
│   ├── useTableColumns.ts # 列配置
│   ├── useSorting.ts      # 排序逻辑
│   └── usePagination.ts   # 分页逻辑
└── types/
    └── table.ts           # 表格相关类型
```

#### 2.2 主组件重构

```vue
<template>
  <div class="ranking-table">
    <n-data-table
      v-bind="tableProps"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
      @update:sorter="handleSorterChange"
    />
    
    <n-empty v-if="isEmpty" description="暂无数据">
      <template #icon>
        <n-icon :size="48" :depth="3">
          <BusinessOutline />
        </n-icon>
      </template>
    </n-empty>
  </div>
</template>

<script setup lang="ts">
import { useTableData } from './composables/useTableData'
import { useTableColumns } from './composables/useTableColumns'
import { useSorting } from './composables/useSorting'
import { usePagination } from './composables/usePagination'

const props = defineProps<{
  items: SpecialOperationItem[]
  total: number
  currentPage: number
  pageSize: number
  loading?: boolean
  materialInfos?: Record<string, MaterialInfo>
}>()

const emit = defineEmits<{
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
  'item-click': [item: SpecialOperationItem]
  'sort-change': [sortInfo: { key: string, order: 'ascend' | 'descend' | false }]
}>()

// 组合式函数
const { tableData, isEmpty } = useTableData(props)
const { columns } = useTableColumns(props, emit)
const { handleSorterChange } = useSorting(emit)
const { paginationConfig, handlePageChange, handlePageSizeChange } = usePagination(props, emit)

// 表格属性
const tableProps = computed(() => ({
  columns: columns.value,
  data: tableData.value,
  loading: props.loading,
  pagination: paginationConfig.value,
  rowKey: (row: any) => row.id,
  scrollX: 1200,
  size: 'small',
  striped: true,
  rowProps: (row: SpecialOperationItem & { rank: number }) => ({
    style: 'cursor: pointer;',
    onClick: () => emit('item-click', row)
  })
}))
</script>
```

## 实施建议

### 阶段一：基础优化（1-2天）
1. 提取子组件（RankBadge、ItemInfo）
2. 优化样式结构
3. 改进类型定义

### 阶段二：性能优化（2-3天）
1. 实现虚拟滚动
2. 添加懒加载
3. 优化渲染函数

### 阶段三：功能增强（3-5天）
1. 添加高级筛选
2. 实现列配置
3. 增加导出功能

## 注意事项

1. **向后兼容**: 保持现有 API 不变
2. **渐进迁移**: 可以逐步替换组件
3. **性能监控**: 关注大数据量下的表现
4. **移动端适配**: 确保响应式设计正常工作
5. **测试覆盖**: 重构后需要完整的测试

## 预期收益

1. **性能提升**: 30-50% 的渲染性能改进
2. **代码质量**: 更好的可维护性和可测试性
3. **用户体验**: 更流畅的交互和更快的响应
4. **开发效率**: 组件化后更容易扩展和修改

## 技术实现细节

### 1. Composables 实现

#### useTableData.ts
```typescript
import { computed } from 'vue'
import type { SpecialOperationItem } from '@/types/specialOperation'

export function useTableData(props: {
  items: SpecialOperationItem[]
  currentPage: number
  pageSize: number
}) {
  const tableData = computed(() => {
    return props.items.map((item, index) => ({
      ...item,
      rank: (props.currentPage - 1) * props.pageSize + index + 1
    }))
  })

  const isEmpty = computed(() => !props.items.length)

  return {
    tableData,
    isEmpty
  }
}
```

#### useTableColumns.ts
```typescript
import { computed, h } from 'vue'
import { NImage } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import type { SpecialOperationItem, MaterialInfo } from '@/types/specialOperation'
import RankBadge from '../components/RankBadge.vue'
import ItemInfo from '../components/ItemInfo.vue'
import MaterialsList from '../components/MaterialsList.vue'

export function useTableColumns(
  props: { materialInfos?: Record<string, MaterialInfo> },
  emit: any
) {
  const columns = computed<DataTableColumns<SpecialOperationItem & { rank: number }>>(() => [
    {
      title: '排名',
      key: 'rank',
      width: 70,
      minWidth: 70,
      align: 'center',
      fixed: 'left',
      render: (row) => h(RankBadge, { rank: row.rank })
    },
    {
      title: '物品',
      key: 'name',
      width: 200,
      minWidth: 180,
      fixed: 'left',
      render: (row) => h(ItemInfo, {
        item: row,
        onClick: () => emit('item-click', row)
      })
    },
    {
      title: '售价',
      key: 'sale_price',
      width: 90,
      minWidth: 80,
      align: 'right',
      sorter: true,
      render: (row) => h('span', {
        class: 'price-text'
      }, row.sale_price?.toLocaleString() || '-')
    },
    {
      title: '成本',
      key: 'cost_price',
      width: 90,
      minWidth: 80,
      align: 'right',
      sorter: true,
      render: (row) => h('span', {
        class: 'cost-text'
      }, row.cost_price?.toLocaleString() || '-')
    },
    {
      title: '手续费',
      key: 'fee',
      width: 90,
      minWidth: 80,
      align: 'right',
      sorter: true,
      render: (row) => h('span', {
        class: 'fee-text'
      }, row.fee?.toLocaleString() || '-')
    },
    {
      title: '保证金',
      key: 'bail',
      width: 90,
      minWidth: 80,
      align: 'right',
      sorter: true,
      render: (row) => h('span', {
        class: 'deposit-text'
      }, row.bail?.toLocaleString() || '-')
    },
    {
      title: '净利润',
      key: 'lirun',
      width: 100,
      minWidth: 90,
      align: 'right',
      sorter: true,
      render: (row) => {
        const profit = row.lirun || 0
        const profitClass = profit > 0 ? 'profit-positive' :
                           profit < 0 ? 'profit-negative' : 'profit-zero'
        return h('span', {
          class: `profit-text ${profitClass}`
        }, profit.toLocaleString())
      }
    },
    {
      title: '生产周期',
      key: 'period',
      width: 90,
      minWidth: 80,
      align: 'center',
      sorter: true,
      render: (row) => h('span', {}, `${row.period || 0}小时`)
    },
    {
      title: '生产数量',
      key: 'per_count',
      width: 90,
      minWidth: 80,
      align: 'center',
      sorter: true,
      render: (row) => h('span', {}, row.per_count || 0)
    },
    {
      title: '每小时利润',
      key: 'profit_per_hour',
      width: 110,
      minWidth: 100,
      align: 'right',
      sorter: true,
      render: (row) => {
        const hourlyProfit = row.profit_per_hour || 0
        const profitClass = hourlyProfit > 0 ? 'profit-positive' :
                           hourlyProfit < 0 ? 'profit-negative' : 'profit-zero'
        return h('span', {
          class: `profit-text ${profitClass}`
        }, hourlyProfit.toLocaleString())
      }
    },
    {
      title: '所需材料',
      key: 'materials',
      width: 220,
      minWidth: 180,
      align: 'center',
      render: (row) => h(MaterialsList, {
        materials: row.materials || '',
        materialInfos: props.materialInfos || {}
      })
    }
  ])

  return { columns }
}
```

### 2. 子组件实现

#### RankBadge.vue
```vue
<template>
  <span :class="badgeClass">{{ rank }}</span>
</template>

<script setup lang="ts">
interface Props {
  rank: number
}

const props = defineProps<Props>()

const badgeClass = computed(() => {
  const baseClass = 'rank-badge'
  if (props.rank <= 3) {
    return `${baseClass} rank-${props.rank}`
  }
  return `${baseClass} rank-normal`
})
</script>

<style scoped>
.rank-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 11px;
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #b45309;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0, #e5e7eb);
  color: #374151;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-3 {
  background: linear-gradient(135deg, #cd7f32, #d97706);
  color: white;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.rank-normal {
  background: var(--n-color-target);
  color: var(--n-text-color-disabled);
}
</style>
```

### 3. 性能优化策略

#### 虚拟滚动配置
```typescript
// 在主组件中
const virtualScrollConfig = computed(() => ({
  enabled: props.items.length > 100,
  itemSize: 48, // 每行高度
  buffer: 10,   // 缓冲区大小
  threshold: 500 // 启用阈值
}))
```

#### 材料信息缓存策略
```typescript
// 材料信息缓存管理
const materialCache = new Map<string, MaterialInfo>()
const loadingMaterials = new Set<string>()

const loadMaterialInfo = async (materialId: string) => {
  if (materialCache.has(materialId) || loadingMaterials.has(materialId)) {
    return materialCache.get(materialId)
  }

  loadingMaterials.add(materialId)
  try {
    const info = await getMaterialInfo(materialId)
    materialCache.set(materialId, info)
    return info
  } finally {
    loadingMaterials.delete(materialId)
  }
}
```

## 迁移指南

### 步骤1：准备工作
1. 备份现有组件
2. 创建新的组件目录结构
3. 安装必要的依赖

### 步骤2：逐步迁移
1. 先迁移样式和基础结构
2. 然后迁移业务逻辑
3. 最后优化性能

### 步骤3：测试验证
1. 单元测试
2. 集成测试
3. 性能测试
4. 用户体验测试

## 风险评估

### 高风险
- 大数据量下的性能表现
- 移动端兼容性
- 现有功能的完整性

### 中风险
- 第三方依赖的兼容性
- 样式的一致性
- 国际化支持

### 低风险
- 代码结构调整
- 类型定义更新
- 文档更新

## 总结

通过这次重构，我们将获得：
1. 更好的代码组织结构
2. 更高的性能表现
3. 更强的可维护性
4. 更好的用户体验

建议采用渐进式重构方案，分阶段实施，确保每个阶段都有明确的目标和验收标准。
