<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    :title="isEdit ? '编辑事件' : '新增事件'"
    size="large"
    :bordered="false"
    :segmented="false"
    :mask-closable="false"
    @close="handleClose"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="24" :x-gap="12">
        <!-- 基本信息 -->
        <n-form-item-gi :span="24" label="事件标题" path="title">
          <n-input
            v-model:value="formData.title"
            placeholder="请输入事件标题"
            maxlength="100"
            show-count
          />
        </n-form-item-gi>

        <n-form-item-gi :span="12" label="事件日期" path="event_date">
          <n-date-picker
            v-model:value="formData.event_date"
            type="date"
            placeholder="选择事件日期"
            style="width: 100%"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="12" label="事件时间" path="event_time">
          <n-time-picker
            v-model:value="formData.event_time"
            placeholder="选择事件时间"
            style="width: 100%"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="12" label="事件类型" path="event_type_id">
          <n-input-group>
            <n-select
              v-model:value="formData.event_type_id"
              :options="eventTypeOptions"
              placeholder="选择事件类型"
              clearable
              filterable
              :loading="loadingEventTypes"
              @update:value="onEventTypeChange"
              style="flex: 1"
            />
            <n-button
              type="primary"
              ghost
              @click="showTypeManager = true"
              :disabled="!savedPassword"
            >
              管理
            </n-button>
          </n-input-group>
        </n-form-item-gi>

        <n-form-item-gi :span="12" label="自定义类型" path="event_type_custom">
          <n-input
            v-model:value="formData.event_type_custom"
            placeholder="自定义事件类型（可选）"
            :disabled="!!formData.event_type_id"
            @input="onCustomTypeInput"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="12" label="重要程度" path="importance_level">
          <n-rate
            v-model:value="formData.importance_level"
            :count="5"
            allow-half
          />
        </n-form-item-gi>

        <n-form-item-gi :span="12" label="所属赛季" path="season">
          <n-select
            v-model:value="formData.season"
            :options="seasonOptions"
            placeholder="选择赛季"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="24" label="事件描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入事件详细描述"
            :rows="4"
            maxlength="1000"
            show-count
          />
        </n-form-item-gi>

        <n-form-item-gi :span="24" label="相关标签" path="tags">
          <n-dynamic-tags
            v-model:value="formData.tags"
            :max="10"
            placeholder="添加标签"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">取消</n-button>
        <n-button
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          {{ isEdit ? '更新' : '保存' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>

  <!-- 事件类型管理弹窗 -->
  <EventTypeManager
    v-model:show="showTypeManager"
    :password="savedPassword"
    @refresh="onTypeManagerRefresh"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'
import * as economicsHistoryApi from '@/api/economicsHistory'
import type { EconomicsHistoryEvent, EventType } from '@/types/economicsHistory'
import { SEASON_OPTIONS } from '@/types/economicsHistory'
import EventTypeManager from './EventTypeManager.vue'

interface Props {
  show: boolean
  event?: EconomicsHistoryEvent | null
  season: string
  savedPassword: string
}

const props = withDefaults(defineProps<Props>(), {
  event: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  save: []
}>()

const message = useMessage()
const formRef = ref<FormInst>()
const saving = ref(false)
const eventTypes = ref<EventType[]>([])
const loadingEventTypes = ref(false)
const showTypeManager = ref(false)

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.event?.id)

// 表单数据
const defaultFormData = () => ({
  title: '',
  event_date: null as number | null,
  event_time: null as number | null,
  event_type_id: null as number | null,
  event_type_custom: '',
  importance_level: 1,
  season: props.season,
  description: '',
  tags: [] as string[]
})

const formData = ref(defaultFormData())

// 选项配置
const seasonOptions = SEASON_OPTIONS

// 事件类型选项
const eventTypeOptions = computed(() => {
  return eventTypes.value
    .filter(type => type && type.status !== 0)
    .map(type => ({
      label: type.type_display_name || '未知类型',
      value: type.id
    }))
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入事件标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度应在2-100字符之间', trigger: 'blur' }
  ],
  event_date: [
    { required: true, message: '请选择事件日期', trigger: 'change', type: 'number' }
  ],
  season: [
    { required: true, message: '请选择所属赛季', trigger: 'change' }
  ],
  importance_level: [
    { required: true, message: '请设置重要程度', trigger: 'change', type: 'number' }
  ],
  // 自定义验证：确保至少选择一种事件类型
  event_type_id: [
    {
      validator: (rule, value) => {
        if (!value && !formData.value.event_type_custom) {
          return new Error('请选择事件类型或输入自定义事件类型')
        }
        return true
      },
      trigger: 'change'
    }
  ],
  event_type_custom: [
    {
      validator: (rule, value) => {
        if (!value && !formData.value.event_type_id) {
          return new Error('请选择事件类型或输入自定义事件类型')
        }
        return true
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const loadEventTypes = async (skipCache = false) => {
  loadingEventTypes.value = true
  try {
    const response = await economicsHistoryApi.getEventTypeList(skipCache)
    if (response.code === 1 && response.data) {
      eventTypes.value = Array.isArray(response.data) ? response.data : []
    } else {
      throw new Error(response.msg || '获取事件类型失败')
    }
  } catch (error: any) {
    console.error('加载事件类型失败:', error)
    message.error(error.message || '加载事件类型失败')
    eventTypes.value = []
  } finally {
    loadingEventTypes.value = false
  }
}

const resetForm = () => {
  formData.value = defaultFormData()
  formData.value.season = props.season
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
}

const fillForm = (event: EconomicsHistoryEvent) => {
  formData.value = {
    title: event.title || '',
    event_date: event.event_date ? new Date(event.event_date).getTime() : null,
    event_time: event.event_time ? new Date(`1970-01-01 ${event.event_time}`).getTime() : null,
    event_type_id: event.event_type?.id || null,
    event_type_custom: event.event_type_custom || '',
    importance_level: event.importance_level || 1,
    season: event.season || props.season,
    description: event.description || '',
    tags: event.tags || []
  }
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    const submitData = {
      ...formData.value,
      event_date: formData.value.event_date ?
        new Date(formData.value.event_date).toISOString().split('T')[0] : '',
      event_time: formData.value.event_time ?
        new Date(formData.value.event_time).toTimeString().split(' ')[0] : null,
      password: props.savedPassword
    }

    let response
    if (isEdit.value) {
      response = await economicsHistoryApi.updateEvent({
        ...submitData,
        id: props.event!.id
      })
    } else {
      response = await economicsHistoryApi.createEvent(submitData)
    }

    if (response.code === 1) {
      message.success(isEdit.value ? '更新成功' : '创建成功')
      emit('save')
      handleClose()
    } else {
      throw new Error(response.msg || '保存失败')
    }
  } catch (error: any) {
    console.error('保存失败:', error)
    message.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  showModal.value = false
  resetForm()
}

// 事件类型选择变化时，清除自定义类型
const onEventTypeChange = (value: number | null) => {
  if (value) {
    formData.value.event_type_custom = ''
  }
}

// 自定义类型输入时，清除事件类型选择
const onCustomTypeInput = (value: string) => {
  if (value.trim()) {
    formData.value.event_type_id = null
  }
}

// 类型管理器刷新后重新加载事件类型
const onTypeManagerRefresh = async () => {
  await loadEventTypes(true)
}

// 监听器
watch(() => props.show, async (show) => {
  if (show) {
    // 先加载事件类型
    await loadEventTypes()

    if (props.event) {
      fillForm(props.event)
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
:deep(.n-card .n-card__content) {
  padding: 12px;
}

:deep(.n-form-item .n-form-item__label) {
  font-weight: 500;
}

:deep(.n-dynamic-tags .n-tag) {
  margin-right: 8px;
  margin-bottom: 4px;
}
</style>