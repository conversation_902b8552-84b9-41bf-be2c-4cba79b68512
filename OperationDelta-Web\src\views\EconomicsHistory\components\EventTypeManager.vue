<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="事件类型管理"
    :style="{ width: '800px', maxWidth: '90vw' }"
    :mask-closable="false"
    @after-leave="resetForm"
  >
    <div class="event-type-manager">
      <!-- 添加/编辑类型 -->
      <n-card :title="editingType ? '编辑类型' : '添加新类型'" size="small" style="margin-bottom: 16px">
        <n-form
          ref="formRef"
          :model="typeForm"
          :rules="rules"
          label-placement="top"
          size="small"
        >
          <n-grid :cols="24" :x-gap="12">
            <n-form-item-gi :span="8" label="类型标识" path="type_name">
              <n-input
                v-model:value="typeForm.type_name"
                placeholder="英文标识，如: market_event"
                @input="onTypeNameInput"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="8" label="显示名称" path="type_display_name">
              <n-input
                v-model:value="typeForm.type_display_name"
                placeholder="中文名称，如: 市场事件"
                @input="onDisplayNameInput"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="4" label="颜色" path="type_color">
              <n-color-picker
                v-model:value="typeForm.type_color"
                :modes="['hex']"
                :show-alpha="false"
                style="width: 100%"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="4" label=" ">
              <n-space>
                <n-button
                  type="primary"
                  @click="handleSaveType"
                  :loading="saving"
                  size="small"
                >
                  {{ editingType ? '更新' : '添加' }}
                </n-button>
                <n-button
                  v-if="editingType"
                  @click="handleCancelEdit"
                  size="small"
                >
                  取消
                </n-button>
              </n-space>
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </n-card>

      <!-- 现有类型列表 -->
      <n-card title="现有类型" size="small">
        <n-data-table
          :columns="columns"
          :data="eventTypes"
          :loading="loading"
          :pagination="false"
          size="small"
          :key="tableKey"
        />
      </n-card>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, h } from 'vue'
import { useMessage, useDialog, type FormInst, type FormRules, type DataTableColumns } from 'naive-ui'
import { NButton, NTag, NSpace, NPopconfirm } from 'naive-ui'
import * as economicsHistoryApi from '@/api/economicsHistory'
import type { EventType } from '@/types/economicsHistory'
import { Storage } from '@/utils/storage'

// Props
interface Props {
  show: boolean
  password: string
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  password: ''
})

// Events
const emit = defineEmits<{
  'update:show': [value: boolean]
  refresh: []
}>()

// 响应式状态
const visible = ref(props.show)
const loading = ref(false)
const saving = ref(false)
const formRef = ref<FormInst | null>(null)
const eventTypes = ref<EventType[]>([])
const editingType = ref<EventType | null>(null)
const tableKey = ref(0)

const message = useMessage()
const dialog = useDialog()

// 类型表单（用于添加和编辑）
const typeForm = reactive({
  type_name: '',
  type_display_name: '',
  type_color: '#1890ff',
  password: ''
})

// 表单验证规则
const rules: FormRules = {
  type_name: [
    { required: true, message: '请输入类型标识', trigger: 'blur' },
    { min: 1, max: 50, message: '类型标识长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-z_]+$/, message: '类型标识只能包含小写字母和下划线', trigger: 'blur' }
  ],
  type_display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 1, max: 50, message: '显示名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 表格列定义
const columns: DataTableColumns<EventType> = [
  {
    title: '显示名称',
    key: 'type_display_name',
    render: (row) => h(NTag, {
      color: { color: row.type_color, textColor: '#fff' }
    }, { default: () => row.type_display_name })
  },
  {
    title: '类型标识',
    key: 'type_name',
    width: 150,
    render: (row) => h('code', {
      style: {
        fontSize: '12px',
        color: 'var(--n-text-color-2)',
        backgroundColor: 'var(--n-color-neutral)',
        padding: '2px 6px',
        borderRadius: '4px'
      }
    }, row.type_name)
  },
  {
    title: '颜色',
    key: 'type_color',
    width: 80,
    render: (row) => h('div', {
      style: {
        width: '20px',
        height: '20px',
        backgroundColor: row.type_color,
        borderRadius: '4px',
        border: '1px solid #d9d9d9'
      }
    })
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render: (row) => row.created_at ? new Date(row.created_at).toLocaleString('zh-CN') : '-'
  },
  {
    title: '操作',
    key: 'actions',
    width: 160,
    render: (row) => h(NSpace, { size: 'small' }, {
      default: () => [
        h(NButton, {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => handleEditType(row)
        }, { default: () => '编辑' }),
        h(NButton, {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDeleteType(row)
        }, { default: () => '删除' })
      ]
    })
  }
]

// 监听props变化
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadEventTypes()
  }
})

watch(visible, (newVal) => {
  emit('update:show', newVal)
})

// 加载事件类型
const loadEventTypes = async (forceRefresh = false) => {
  loading.value = true
  try {
    // 在管理器中刷新时跳过缓存，确保获取最新数据
    const response = await economicsHistoryApi.getEventTypeList(forceRefresh)
    if (response.code === 1) {
      // 强制更新数组引用以触发响应式更新
      const newData = response.data || []
      eventTypes.value = [...newData]
      // 强制刷新表格
      tableKey.value++
      console.log('事件类型列表已更新:', eventTypes.value.length, '个类型', forceRefresh ? '(跳过缓存)' : '(使用缓存)')
    } else {
      message.error('加载事件类型失败: ' + (response.msg || '未知错误'))
      console.error('加载事件类型失败:', response)
    }
  } catch (error: any) {
    message.error('加载事件类型失败: ' + (error.message || '网络错误'))
    console.error('加载事件类型异常:', error)
  } finally {
    loading.value = false
  }
}

// 保存类型（添加或编辑）
const handleSaveType = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (!props.password) {
      message.error('请先在事件编辑器中输入密码')
      return
    }

    saving.value = true

    let response
    if (editingType.value) {
      // 编辑模式
      response = await economicsHistoryApi.updateEventType({
        id: editingType.value.id,
        type_display_name: typeForm.type_display_name,
        type_color: typeForm.type_color,
        password: props.password
      })
    } else {
      // 添加模式
      response = await economicsHistoryApi.createEventType({
        type_name: typeForm.type_name,
        type_display_name: typeForm.type_display_name,
        type_color: typeForm.type_color,
        password: props.password
      })
    }

    if (response.code === 1) {
      const isEdit = !!editingType.value
      message.success(isEdit ? '更新成功' : '添加成功')

      // 重置表单（在刷新之前，避免状态混乱）
      resetForm()

      // 清理缓存
      Storage.clearEconomicsHistoryCache()

      // 重新加载列表（跳过缓存）
      await loadEventTypes(true)

      // 通知父组件刷新
      emit('refresh')

      console.log(isEdit ? '事件类型更新完成' : '事件类型创建完成')
    } else {
      throw new Error(response.msg || (editingType.value ? '更新失败' : '添加失败'))
    }
  } catch (error: any) {
    message.error(error.message || (editingType.value ? '更新失败' : '添加失败'))
  } finally {
    saving.value = false
  }
}

// 删除类型
const handleDeleteType = (eventType: EventType) => {
  if (!props.password) {
    message.error('请先在事件编辑器中输入密码')
    return
  }
  
  dialog.warning({
    title: '删除确认',
    content: `确定要删除事件类型"${eventType.type_display_name}"吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const response = await economicsHistoryApi.deleteEventType({
          id: eventType.id,
          password: props.password
        })
        
        if (response.code === 1) {
          message.success('删除成功')
          // 清理缓存
          Storage.clearEconomicsHistoryCache()
          // 重新加载列表（跳过缓存）
          await loadEventTypes(true)
          // 通知父组件刷新
          emit('refresh')
        } else {
          throw new Error(response.msg || '删除失败')
        }
      } catch (error: any) {
        message.error(error.message || '删除失败')
      }
    }
  })
}

// 编辑类型
const handleEditType = (eventType: EventType) => {
  editingType.value = eventType
  typeForm.type_name = eventType.type_name || ''
  typeForm.type_display_name = eventType.type_display_name
  typeForm.type_color = eventType.type_color
}

// 取消编辑
const handleCancelEdit = () => {
  editingType.value = null
  resetForm()
}

// 重置表单
const resetForm = () => {
  editingType.value = null
  typeForm.type_name = ''
  typeForm.type_display_name = ''
  typeForm.type_color = '#1890ff'
  formRef.value?.restoreValidation()
}

// 输入处理函数
const onTypeNameInput = (value: string) => {
  // 确保 type_name 只包含小写字母和下划线
  typeForm.type_name = value.toLowerCase().replace(/[^a-z_]/g, '')
}

const onDisplayNameInput = (value: string) => {
  // 如果是添加模式且 type_name 为空，自动生成 type_name
  if (!editingType.value && !typeForm.type_name) {
    // 简单的中文转英文映射
    const mapping: Record<string, string> = {
      '货币政策': 'monetary_policy',
      '财政政策': 'fiscal_policy',
      '市场事件': 'market_event',
      '监管变化': 'regulatory_change',
      '经济指标': 'economic_indicator',
      '国际事件': 'international_event',
      '赛季开始': 'season_start',
      '更新': 'update',
      '市场': 'market',
      '政策': 'policy',
      '修复': 'bug_fix',
      '价格调整': 'price_adjustment',
      '新物品': 'new_item',
      '平衡调整': 'balance_change',
      '重大事件': 'major_event'
    }

    // 尝试映射，如果没有映射则生成简单的标识符
    const mapped = mapping[value]
    if (mapped) {
      typeForm.type_name = mapped
    } else if (value) {
      // 生成简单的英文标识符
      typeForm.type_name = value
        .replace(/[\u4e00-\u9fa5]/g, '') // 移除中文字符
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_') // 非字母数字替换为下划线
        .replace(/_+/g, '_') // 多个下划线合并为一个
        .replace(/^_|_$/g, '') // 移除首尾下划线
        || 'custom_type'
    }
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.event-type-manager {
  max-height: 600px;
  overflow-y: auto;
}
</style>
