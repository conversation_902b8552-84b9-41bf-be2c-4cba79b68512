// 三角洲经济学编年史API

import { apiClient } from './index'
import type { ApiResponse } from './types/common'
import type {
  EconomicsHistoryEvent,
  EventType,
  CreateEventRequest,
  UpdateEventRequest,
  DeleteEventRequest,
  CreateEventTypeRequest,
  UpdateEventTypeRequest,
  DeleteEventTypeRequest
} from '@/types/economicsHistory'

// API端点常量
export const ECONOMICS_HISTORY_API = {
  GET_SEASON_HISTORY: '/economicsHistory/getSeasonHistory',
  CREATE_EVENT: '/economicsHistory/createEvent',
  UPDATE_EVENT: '/economicsHistory/updateEvent',
  DELETE_EVENT: '/economicsHistory/deleteEvent',
  GET_SEASON_LIST: '/economicsHistory/getSeasonList',
  GET_EVENT_TYPE_LIST: '/economicsHistory/getEventTypeList',
  CREATE_EVENT_TYPE: '/economicsHistory/createEventType',
  UPDATE_EVENT_TYPE: '/economicsHistory/updateEventType',
  DELETE_EVENT_TYPE: '/economicsHistory/deleteEventType',
  VERIFY_PASSWORD: '/economicsHistory/verifyPassword'
}

/**
 * 获取指定赛季编年史
 */
export function getSeasonHistory(season: string, skipCache = false): Promise<ApiResponse<EconomicsHistoryEvent[]>> {
  return apiClient.post(ECONOMICS_HISTORY_API.GET_SEASON_HISTORY, { season }, { skipCache })
}

/**
 * 创建编年史事件 (包含密码验证) - 跳过缓存
 */
export function createEvent(data: CreateEventRequest): Promise<ApiResponse<{ id: number }>> {
  return apiClient.post(ECONOMICS_HISTORY_API.CREATE_EVENT, data, { skipCache: true })
}

/**
 * 更新编年史事件 (包含密码验证) - 跳过缓存
 */
export function updateEvent(data: UpdateEventRequest): Promise<ApiResponse<any>> {
  return apiClient.post(ECONOMICS_HISTORY_API.UPDATE_EVENT, data, { skipCache: true })
}

/**
 * 删除编年史事件 (包含密码验证) - 跳过缓存
 */
export function deleteEvent(data: DeleteEventRequest): Promise<ApiResponse<any>> {
  return apiClient.post(ECONOMICS_HISTORY_API.DELETE_EVENT, data, { skipCache: true })
}

/**
 * 获取所有赛季列表
 */
export function getSeasonList(): Promise<ApiResponse<string[]>> {
  return apiClient.post(ECONOMICS_HISTORY_API.GET_SEASON_LIST)
}

/**
 * 获取事件类型列表
 */
export function getEventTypeList(skipCache = false): Promise<ApiResponse<EventType[]>> {
  return apiClient.post(ECONOMICS_HISTORY_API.GET_EVENT_TYPE_LIST, {}, { skipCache })
}

/**
 * 创建事件类型 - 跳过缓存
 */
export function createEventType(data: CreateEventTypeRequest): Promise<ApiResponse<{ id: number }>> {
  return apiClient.post(ECONOMICS_HISTORY_API.CREATE_EVENT_TYPE, data, { skipCache: true })
}

/**
 * 更新事件类型 - 跳过缓存
 */
export function updateEventType(data: UpdateEventTypeRequest): Promise<ApiResponse<any>> {
  return apiClient.post(ECONOMICS_HISTORY_API.UPDATE_EVENT_TYPE, data, { skipCache: true })
}

/**
 * 删除事件类型 - 跳过缓存
 */
export function deleteEventType(data: DeleteEventTypeRequest): Promise<ApiResponse<any>> {
  return apiClient.post(ECONOMICS_HISTORY_API.DELETE_EVENT_TYPE, data, { skipCache: true })
}

/**
 * 验证编辑密码
 */
export function verifyPassword(password: string): Promise<ApiResponse<any>> {
  return apiClient.post(ECONOMICS_HISTORY_API.VERIFY_PASSWORD, { password })
}